import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, getDocs, orderBy, query, where } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";
import { generateNames } from "@utils/helpers";
import { firebaseService } from "../../service/firebase.service";

const initialState = {
  error: null,
  isLoading: false,
  nurses: [],
  caregivers: [],
  clients: [],
};

const NURSE_DEPARTMENT = { id: "nurse", value: "nurse", label: "Registered Nurse" };
const CAREGIVER_DEPARTMENT = { id: "caregiver", value: "caregiver", label: "Caregiver" };

// THUNK TO GET ALL USERS
export const getAllUsers = createAsyncThunk("users/getAllUsers", async (_, { rejectWithValue, fulfillWithValue }) => {
  try {
    const q = query(collection(db, COLLECTIONS.USERS), orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);
    const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
    const payload = {
      nurses: arr
        .filter((user) => user?.role === "NURSE")
        ?.map((item) => ({
          ...item,
          firstName: generateNames(item?.name)?.firstName,
          lastName: generateNames(item?.name)?.lastName,
          department: [NURSE_DEPARTMENT],
        })),
      caregivers: arr
        .filter((user) => user?.role === "CAREGIVER")
        ?.map((item) => ({
          ...item,
          firstName: generateNames(item?.name)?.firstName,
          lastName: generateNames(item?.name)?.lastName,
          department: [CAREGIVER_DEPARTMENT],
        })),
      clients: arr
        .filter((user) => user?.role === "CLIENT")
        ?.map((item) => ({
          ...item,
          firstName: generateNames(item?.name)?.firstName,
          lastName: generateNames(item?.name)?.lastName,
          reg: new Date(item?.createdAt?.toDate()),
          age: new Date().getFullYear() - new Date(item?.dob)?.getFullYear(),
        })),
    };
    return fulfillWithValue(payload);
  } catch (error) {
    return rejectWithValue(error);
  }
});

// THUNK TO GET ALL CAREGIVERS OF NURSE
export const getAllCaregiversOfNurse = createAsyncThunk(
  "users/getAllCaregiversOfNurse",
  async (_, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.USERS),
        where("role", "==", "CAREGIVER"),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      const payload = {
        caregivers: arr
          .filter((user) => user?.role === "CAREGIVER")
          ?.map((item) => ({
            ...item,
            firstName: generateNames(item?.name)?.firstName,
            lastName: generateNames(item?.name)?.lastName,
            department: [CAREGIVER_DEPARTMENT],
          })),
      };
      return fulfillWithValue(payload);
    } catch (error) {
      console.log("GET CAREGIVER OF NURSE >>", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO GET ALL CLIENTS OF NURSE
export const getAllPatientsOfNurse = createAsyncThunk(
  "users/getAllPatientsOfNurse",
  async (nurseId, { rejectWithValue, fulfillWithValue }) => {
    try {
      const q = query(
        collection(db, COLLECTIONS.USERS),
        where("role", "==", "CLIENT"),
        where("assignedNurse", "==", nurseId),
        orderBy("createdAt", "desc"),
      );
      const snapshot = await getDocs(q);
      const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      const payload = {
        clients: arr
          .filter((user) => user?.role === "CLIENT")
          ?.map((item) => ({
            ...item,
            firstName: generateNames(item?.name)?.firstName,
            lastName: generateNames(item?.name)?.lastName,
            reg: new Date(item?.createdAt?.toDate()),
            age: new Date().getFullYear() - new Date(item?.dob)?.getFullYear(),
          })),
      };
      return fulfillWithValue(payload);
    } catch (error) {
      console.log("GET CLIENTS OF NURSE >>", error);
      return rejectWithValue(error);
    }
  },
);

// THUNK TO ASSIGN ADMIN PERMISSION
export const assignAdminPermission = createAsyncThunk(
  "users/assignAdminPermission",
  async ({ userId, userEmail, userName }, { rejectWithValue }) => {
    try {
      const response = await firebaseService.assignAdminPermission({
        userId,
        userEmail,
        userName,
      });

      return {
        userId,
        userEmail,
        userName,
        ...response.data,
      };
    } catch (error) {
      console.log("ASSIGN ADMIN PERMISSION THUNK >>", error);

      let errorMessage = "Failed to assign admin permissions";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      return rejectWithValue(errorMessage);
    }
  }
);

// THUNK TO CREATE ADMIN USER
export const createAdminUser = createAsyncThunk(
  "users/createAdminUser",
  async ({ email, password, name }, { rejectWithValue }) => {
    try {
      const response = await firebaseService.createAdminUser({
        email,
        password,
        name,
      });

      return {
        email,
        name,
        ...response.data,
      };
    } catch (error) {
      console.log("CREATE ADMIN USER THUNK >>", error);

      let errorMessage = "Failed to create admin user";
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      return rejectWithValue(errorMessage);
    }
  }
);

const usersSlice = createSlice({
  name: "users",
  initialState,
  reducers: {
    addNewUserAction: (state, { payload }) => {
      if (payload?.role === "NURSE") {
        state.nurses = [...state.nurses, payload];
      }
      if (payload?.role === "CAREGIVER") {
        state.caregivers = [...state.caregivers, payload];
      }
      if (payload?.role === "CLIENT") {
        state.clients = [...state.clients, payload];
      }
      return state;
    },
    updateUserAction: (state, { payload }) => {
      if (payload?.role === "NURSE") {
        const index = state.nurses.findIndex((item) => item?.id === payload?.id);
        state.nurses[index] = {
          ...state.nurses[index],
          ...payload,
        };
      }
      if (payload?.role === "CAREGIVER") {
        const index = state.caregivers.findIndex((item) => item?.id === payload?.id);
        state.caregivers[index] = {
          ...state.caregivers[index],
          ...payload,
        };
      }
      if (payload?.role === "CLIENT") {
        const index = state.clients.findIndex((item) => item?.id === payload?.id);
        state.clients[index] = {
          ...state.clients[index],
          ...payload,
        };
      }
      return state;
    },
    promoteUserToAdminAction: (state, { payload }) => {
      const { userId } = payload;

      // Find and remove user from their current role array
      // Check nurses array
      const nurseIndex = state.nurses.findIndex((user) => user.id === userId);
      if (nurseIndex !== -1) {
        state.nurses.splice(nurseIndex, 1);
      }

      // Check caregivers array
      const caregiverIndex = state.caregivers.findIndex((user) => user.id === userId);
      if (caregiverIndex !== -1) {
        state.caregivers.splice(caregiverIndex, 1);
      }

      // Check clients array
      const clientIndex = state.clients.findIndex((user) => user.id === userId);
      if (clientIndex !== -1) {
        state.clients.splice(clientIndex, 1);
      }

      // Note: We don't add the user to any array since admins are not displayed in the regular user lists
      // The user will be removed from the UI and their role will be updated in the database

      return state;
    },
  },
  extraReducers: (builder) => {
    builder
      // GET ALL USERS (preferred for admin)
      .addCase(getAllUsers.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllUsers.fulfilled, (state, { payload }) => {
        state.caregivers = payload.caregivers;
        state.clients = payload.clients;
        state.nurses = payload.nurses;
        state.isLoading = false;
      })
      .addCase(getAllUsers.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET CAREGIVERS FOR NURSE
      .addCase(getAllCaregiversOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllCaregiversOfNurse.fulfilled, (state, { payload }) => {
        state.caregivers = payload.caregivers;
        state.isLoading = false;
      })
      .addCase(getAllCaregiversOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // GET CLIENTS FOR NURSE
      .addCase(getAllPatientsOfNurse.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllPatientsOfNurse.fulfilled, (state, { payload }) => {
        state.clients = payload.clients;
        state.isLoading = false;
      })
      .addCase(getAllPatientsOfNurse.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // ASSIGN ADMIN PERMISSION
      .addCase(assignAdminPermission.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(assignAdminPermission.fulfilled, (state, { payload }) => {
        state.isLoading = false;
        // Remove the user from their current role array since they're now an admin
        usersSlice.caseReducers.promoteUserToAdminAction(state, { payload });
      })
      .addCase(assignAdminPermission.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // CREATE ADMIN USER
      .addCase(createAdminUser.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(createAdminUser.fulfilled, (state) => {
        state.isLoading = false;
        // Admin users are not added to any specific array since they don't appear in regular user lists
      })
      .addCase(createAdminUser.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const { updateUserAction, addNewUserAction, promoteUserToAdminAction } = usersSlice.actions;

export default usersSlice;
